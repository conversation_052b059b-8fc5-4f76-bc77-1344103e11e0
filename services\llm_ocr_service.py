import lmstudio as lms
import base64
import io
import logging
from PIL import Image
from typing import Optional, Dict, Any
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("LLMOCRService")

class LMStudioOCRService:
    """
    OCR service using LM Studio SDK with vision-capable LLM models.
    """

    def __init__(self,
                 model_name: Optional[str] = None,
                 host: str = "localhost:1234",
                 max_tokens: int = 2048,
                 temperature: float = 0.1):
        """
        Initialize LM Studio OCR service.

        Args:
            model_name: Name of the vision-capable model to use
            host: LM Studio server host and port
            max_tokens: Maximum tokens for response
            temperature: Temperature for text generation
        """
        self.model_name = model_name
        self.host = host
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.client = None
        self.model = None

    def _initialize_client(self):
        """Initialize LM Studio client and model."""
        try:
            # Method 1: Try creating a client directly (most reliable)
            logger.info(f"Attempting to connect to LM Studio at {self.host}")
            from lmstudio import Client

            # Create client instance with api_host parameter
            self.client = Client(api_host=self.host)

            # Get the LLM model
            if self.model_name:
                self.model = self.client.llm.model(self.model_name)
            else:
                # Use the default loaded model
                self.model = self.client.llm.model()

            logger.info(f"LM Studio client initialized successfully on {self.host}")
            return True

        except Exception as e:
            logger.error(f"Direct client initialization failed: {str(e)}")

            # Method 2: Try using the convenience API
            try:
                logger.info("Trying convenience API initialization...")

                # Reset any existing default client first
                try:
                    import lmstudio.sync_api as sync_api
                    sync_api._reset_default_client()
                except:
                    pass

                # Configure the default client with the specified host
                lms.configure_default_client(self.host)

                # Get the LLM model
                if self.model_name:
                    self.model = lms.llm(self.model_name)
                else:
                    # Use the default loaded model
                    self.model = lms.llm()

                logger.info("Convenience API initialization successful")
                return True

            except Exception as convenience_error:
                logger.error(f"Convenience API initialization failed: {convenience_error}")

                # Method 3: Try the simple approach without host specification
                try:
                    logger.info("Trying simple initialization (default host)...")
                    self.model = lms.llm()
                    logger.info("Simple initialization successful")
                    return True
                except Exception as simple_error:
                    logger.error(f"Simple initialization also failed: {simple_error}")
                    return False

    def _image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 string."""
        buffer = io.BytesIO()
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image.save(buffer, format='JPEG', quality=95)
        img_bytes = buffer.getvalue()
        return base64.b64encode(img_bytes).decode('utf-8')

    def _create_ocr_prompt(self, language_hint: str = "Khmer and English") -> str:
        """Create a prompt for OCR task."""
        return f"""You are an expert OCR (Optical Character Recognition) system. Please extract ALL text from the provided image with high accuracy.

Instructions:
1. Extract ALL visible text from the image, including {language_hint} text
2. Maintain the original layout and structure as much as possible
3. If you see an ID card or official document, pay special attention to:
   - Names (in both Khmer and English if present)
   - ID numbers (typically 12 digits)
   - Dates (birth date, issue date, expiry date)
   - Gender/Sex
   - Address information
   - Any other official information

4. Format your response as follows:
   - First, provide the raw extracted text exactly as it appears
   - Then, if it's an ID card, provide structured information

Please extract the text now:"""

    def extract_text_from_image(self, image: Image.Image, language_hint: str = "Khmer and English") -> Dict[str, Any]:
        """
        Extract text from image using LM Studio LLM with image description.
        Note: This is a simplified version that uses text-based LLM interaction.
        For full vision model support, ensure you have a vision-capable model loaded in LM Studio.

        Args:
            image: PIL Image object
            language_hint: Hint about expected languages in the image

        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            # Initialize client if not already done
            if not self.model:
                if not self._initialize_client():
                    raise HTTPException(status_code=500, detail="Failed to initialize LM Studio client")

            # For now, we'll create a prompt that asks the LLM to help with OCR
            # In a production environment, you would use a vision-capable model
            prompt = f"""You are an expert OCR assistant. I need help extracting text from a Cambodian ID card image.

The image contains text in {language_hint}. Please help me understand what information should be extracted from a typical Cambodian ID card:

1. Full Name (in Khmer and English)
2. ID Number (12 digits)
3. Date of Birth
4. Gender/Sex
5. Address
6. Issue Date
7. Expiry Date

Based on typical Cambodian ID card layouts, please provide a structured response format that I can use to parse OCR results.

Note: This is a placeholder implementation. For actual image processing, you would need:
- A vision-capable model loaded in LM Studio
- Proper image input handling
- Direct image-to-text processing

Please provide guidance on the expected text patterns and structure."""

            # Prepare the chat
            chat = lms.Chat()
            chat.add_user_message(prompt)

            # Make the prediction
            try:
                response = self.model.respond(chat)
                extracted_text = str(response).strip()
            except Exception as model_error:
                logger.error(f"Model response error: {model_error}")
                # Fallback response
                extracted_text = f"LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: {image.size}, Mode: {image.mode}"

            logger.info(f"LM Studio service called successfully")

            return {
                "extracted_text": extracted_text,
                "model_used": self.model_name or "default",
                "confidence": 0.8,  # Lower confidence for placeholder implementation
                "method": "llm_text_based",
                "note": "This is a placeholder implementation. For full OCR, use a vision-capable model."
            }

        except Exception as e:
            logger.error(f"Error in LM Studio OCR: {str(e)}")
            raise HTTPException(status_code=500, detail=f"LM Studio OCR failed: {str(e)}")

    def extract_text_simple(self, image: Image.Image) -> str:
        """
        Simple text extraction that returns only the text string.

        Args:
            image: PIL Image object

        Returns:
            Extracted text as string
        """
        try:
            result = self.extract_text_from_image(image)
            return result.get("extracted_text", "")
        except Exception as e:
            logger.error(f"Simple text extraction failed: {str(e)}")
            return ""

# Global instance
llm_ocr_service = LMStudioOCRService()
