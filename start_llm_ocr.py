#!/usr/bin/env python3
"""
Startup script for LM Studio OCR application.
This script helps set up and start the OCR service with LM Studio integration.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LLMOCRStartup")

def check_requirements():
    """Check if all requirements are installed."""
    logger.info("Checking requirements...")

    try:
        import fastapi
        import uvicorn
        import lmstudio
        import PIL
        logger.info("✅ All required packages are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing required package: {e}")
        logger.info("Please run: pip install -r requirements.txt")
        return False

def check_lm_studio():
    """Check if LM Studio is accessible."""
    logger.info("Checking LM Studio connection...")

    try:
        import lmstudio as lms

        # Try to connect to LM Studio
        try:
            lms.configure_default_client("localhost:1234")
            model = lms.llm()
            logger.info("✅ LM Studio is accessible")
            return True
        except Exception as e:
            logger.warning(f"⚠️  LM Studio connection failed: {e}")
            logger.info("Please ensure:")
            logger.info("1. LM Studio is running")
            logger.info("2. A model is loaded")
            logger.info("3. Server is running on localhost:1234")
            return False

    except ImportError:
        logger.error("❌ LM Studio SDK not installed")
        return False

def setup_environment():
    """Set up environment configuration."""
    logger.info("Setting up environment...")

    env_file = Path(".env")
    env_example = Path(".env.example")

    if not env_file.exists() and env_example.exists():
        logger.info("Creating .env file from .env.example")
        env_file.write_text(env_example.read_text())
        logger.info("✅ Environment file created")
        logger.info("Please edit .env file with your LM Studio configuration")
    elif env_file.exists():
        logger.info("✅ Environment file already exists")
    else:
        logger.warning("⚠️  No environment configuration found")

    return True

def run_tests():
    """Run basic tests to verify setup."""
    logger.info("Running basic tests...")

    try:
        result = subprocess.run([sys.executable, "test_llm_ocr.py"],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            logger.info("✅ Tests passed successfully")
            return True
        else:
            logger.warning("⚠️  Some tests failed")
            logger.info("Test output:")
            logger.info(result.stdout)
            if result.stderr:
                logger.error(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        logger.warning("⚠️  Tests timed out")
        return False
    except FileNotFoundError:
        logger.warning("⚠️  Test script not found")
        return False
    except Exception as e:
        logger.error(f"❌ Error running tests: {e}")
        return False

def start_server():
    """Start the FastAPI server."""
    logger.info("Starting FastAPI server...")

    try:
        # Start the server
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Error starting server: {e}")

def main():
    """Main startup routine."""
    logger.info("=" * 60)
    logger.info("LM Studio OCR Application Startup")
    logger.info("=" * 60)

    # Check requirements
    if not check_requirements():
        logger.error("❌ Requirements check failed")
        return False

    # Setup environment
    if not setup_environment():
        logger.error("❌ Environment setup failed")
        return False

    # Check LM Studio
    lm_studio_ok = check_lm_studio()
    if not lm_studio_ok:
        logger.warning("⚠️  LM Studio not accessible - OCR may fall back to Tesseract")

    # Run tests
    logger.info("\n" + "=" * 40)
    logger.info("Running System Tests")
    logger.info("=" * 40)

    tests_ok = run_tests()

    # Summary
    logger.info("\n" + "=" * 40)
    logger.info("Startup Summary")
    logger.info("=" * 40)

    logger.info(f"Requirements: ✅ OK")
    logger.info(f"Environment: ✅ OK")
    logger.info(f"LM Studio: {'✅ OK' if lm_studio_ok else '⚠️  WARNING'}")
    logger.info(f"Tests: {'✅ OK' if tests_ok else '⚠️  WARNING'}")

    if lm_studio_ok and tests_ok:
        logger.info("\n🎉 System is ready! Starting server...")
        start_server()
    else:
        logger.warning("\n⚠️  System has warnings but will attempt to start...")
        logger.info("Starting server in 5 seconds... (Ctrl+C to cancel)")

        try:
            import time
            time.sleep(5)
            start_server()
        except KeyboardInterrupt:
            logger.info("Startup cancelled by user")
            return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
