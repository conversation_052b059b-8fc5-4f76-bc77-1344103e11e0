#!/usr/bin/env python3
"""
Simple test for LM Studio connection.
"""

import lmstudio as lms

def test_simple_connection():
    """Test simple LM Studio connection."""
    try:
        print("Testing LM Studio connection...")
        
        # Try to get a model
        model = lms.llm()
        print("✅ Successfully connected to LM Studio!")
        
        # Try a simple completion
        try:
            response = model.complete("Hello, this is a test. Please respond with 'Connection successful!'")
            print(f"✅ Model response: {response}")
            return True
        except Exception as e:
            print(f"⚠️  Model response failed: {e}")
            return True  # Connection worked, just response failed
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_connection()
    print(f"\nTest result: {'PASS' if success else 'FAIL'}")
